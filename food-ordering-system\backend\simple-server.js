const express = require('express');
const dotenv = require('dotenv');
const connectDatabase = require("./config/database");

console.log("Starting simple server...");

// Load config
dotenv.config({path:"backend/config/config.env"});
console.log("Config loaded, PORT:", process.env.PORT);

// Connect to database
console.log("Connecting to database...");
connectDatabase();

const app = express();

app.get('/', (req, res) => {
    res.json({ message: 'Backend server is running!' });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
    console.log(`Simple server is running on port ${PORT}`);
});
