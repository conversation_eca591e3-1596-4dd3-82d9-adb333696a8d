{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@material-ui/core": "^4.12.4", "@material-ui/data-grid": "^4.0.0-alpha.37", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "^4.0.0-alpha.60", "@stripe/react-stripe-js": "^1.16.5", "@stripe/stripe-js": "^1.49.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "axios": "^0.21.1", "chart.js": "^3.8.0", "country-state-city": "^3.0.1", "overlay-navbar": "^1.0.4", "react": "^17.0.2", "react-alert": "^7.0.3", "react-alert-template-basic": "^1.0.2", "react-chartjs-2": "^3.3.0", "react-dom": "^17.0.2", "react-helmet": "^6.1.0", "react-icons": "^4.2.0", "react-js-pagination": "^3.0.3", "react-material-ui-carousel": "^2.3.11", "react-rating-stars-component": "^2.2.0", "react-redux": "^7.2.4", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "redux": "^4.1.1", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^2.3.0", "web-vitals": "^1.1.2", "webfontloader": "^1.6.28"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}