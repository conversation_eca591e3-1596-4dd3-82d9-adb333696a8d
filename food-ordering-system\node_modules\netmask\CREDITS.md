Credits
=======

These credits refer to the contributors to this repository:

[@rs](https://github.com/rs) - maintainer

[@ryanrolds](https://github.com/ryanrolds) - Now with 0.4.x support. #2

[@palmerabollo](https://github.com/palmerabollo) - Expressions "*******/" are not valid #6

[@steve-jansen](https://github.com/steve-jansen) - fixes typo in readme.md #7

[@jksdua](https://github.com/jksdua) - Added forEach helper to allow looping through usable IP addresses #9

[@gmiroshnykov](https://github.com/gmiroshnykov) - Tiny typo fix #10

[@TooTallNate](https://github.com/TooTallNate) - package: move "coffee-script" to devDependencies #11, README: fix small typo #12

[@yorkie](https://github.com/yorkie) - more rigid check for Netmask.constructor #13

[@runk](https://github.com/runk) - fix contains method for netmasks #18

[@yvesago](https://github.com/yvesago) - a patch with mocha test to fix /31 and /32 block.contains #20

[@meteormatt](https://github.com/meteormatt) - The comment in README.md is wrong #22

[@dschenkelman](https://github.com/dschenkelman) - Avoid large memory allocations when doing forEach in case netmask is large (e.g. /8) #34

[@sickcodes](https://github.com/sickcodes), [@kaoudis](https://github.com/kaoudis), [@Koroeskohr](https://github.com/Koroeskohr), [@nicksahler](https://github.com/nicksahler) - adds CREDITS, plus mocha tests for transpiled node #36
