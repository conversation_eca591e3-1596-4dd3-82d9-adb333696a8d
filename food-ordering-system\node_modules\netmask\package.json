{"author": "<PERSON> <<EMAIL>>", "name": "netmask", "description": "Parse and lookup IP network blocks", "version": "2.0.2", "homepage": "https://github.com/rs/node-netmask", "bugs": "https://github.com/rs/node-netmask/issues", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/rs/node-netmask.git"}, "keywords": ["net", "mask", "ip", "network", "cidr", "netmask", "subnet", "ipcalc"], "main": "./lib/netmask", "scripts": {"prepublish": "coffee -c lib/*.coffee", "test": "coffee -c lib/*.coffee && vows --spec test/* && mocha tests/*"}, "engines": {"node": ">= 0.4.0"}, "devDependencies": {"coffee-script": ">=1.2.0", "mocha": "^8.3.2", "vows": "*"}}