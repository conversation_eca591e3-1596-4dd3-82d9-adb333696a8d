{"name": "ecommerce", "version": "1.0.0", "description": "", "main": "backend/server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node backend/server.js  --ignore client", "dev": "nodemon backend/server.js"}, "author": "p<PERSON>ad", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "cloudinary": "^1.30.0", "cookie-parser": "^1.4.6", "dotenv": "^10.0.0", "express": "^4.17.1", "express-fileupload": "^1.4.0", "jsonwebtoken": "^8.5.1", "mongoose": "^6.0.12", "nodemailer": "^6.7.2", "react-scripts": "^5.0.1", "stripe": "^11.15.0", "validator": "^13.7.0"}}