console.log("Loading app.js...");
const express=require('express');
console.log("Express loaded");
const errorMiddleware=require('./middleware/error')
console.log("Error middleware loaded");
const app=express();
console.log("Express app created");
const cookieParser=require('cookie-parser');
const bodyParser = require("body-parser");
const fileUpload = require("express-fileupload");
const dotenv = require("dotenv")


// Config
// if (process.env.NODE_ENV !== "PRODUCTION") {
//     require("dotenv").config({ path: "backend/config/config.env" });
//   }

dotenv.config({ path: "backend/config/config.env" });

app.use(express.json());
app.use(cookieParser());
app.use(bodyParser.urlencoded({extended:true }));
app.use(fileUpload());

//Route Imports
const product= require("./routes/productRoute");
const user =require("./routes/userRoute")
const order = require("./routes/orderRoute")

const payment = require("./routes/paymentRoute")


app.use("/api/v1",product);
app.use('/api/v1',user)
app.use('/api/v1',order)
app.use('/api/v1',payment)

//MiddleWare For Errors...
app.use(errorMiddleware); 

module.exports=app;