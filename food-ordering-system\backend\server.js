console.log("Starting server...");
const app=require('./app');
console.log("App loaded");
const dotenv=require('dotenv');
console.log("Dotenv loaded");
const connectDatabase=require("./config/database");
console.log("Database module loaded");
const cloudinary = require("cloudinary");
console.log("Cloudinary loaded");

//Handelling Uncaught Exception
process.on("uncaughtException",(err)=>{
    console.log(`Error:${err.message}`);
    console.log(`Shutting Down the Server due to Handelling Uncaught Exception`);
    process.exit(1);
});

//config
console.log("Loading config...");
dotenv.config({path:"backend/config/config.env"});
console.log("Config loaded, PORT:", process.env.PORT);

//Connecting to Database
console.log("Connecting to database...");
connectDatabase();
console.log("Database connection initiated");
cloudinary.config({
cloud_name: process.env.CLOUDINARY_NAME,
api_key:process.env.CLOUDINARY_API_KEY,
api_secret: process.env.CLOUDINARY_API_SECRET
});


const server=app.listen(process.env.PORT,()=>
{
    console.log(`Server is working on port http:localhost ${process.env.PORT}`);
});

//Unhandled Promises Rejections...
process.on("unhandledRejection",err=>{
    console.log(`Error:${err.message}`);
    console.log("Shutting Down the Server due to unhandled  promises Rejections");
    server.close(()=>{
        process.exit(1);
    });
});
